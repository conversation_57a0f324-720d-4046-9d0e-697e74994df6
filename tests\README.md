# Arbitrage Bot Unit Tests

This directory contains comprehensive unit tests for the arbitrage bot, focusing on validating the ArbitrageSignal model and Telegram message template formatting.

## Test Coverage

### `test_arbitrage_signal_template.py`

This test suite validates the ArbitrageSignal model and message template formatting with the following test cases:

#### ✅ **Data Validation Tests**

- **`test_valid_signal_creation`** - Validates that ArbitrageSignal objects can be created with all required fields
- **`test_signal_data_types`** - Ensures all fields have correct data types (int, float, str, datetime, bool)
- **`test_required_fields_not_empty`** - Verifies critical fields are not null or empty

#### ✅ **Template Formatting Tests**

- **`test_template_formatting_with_valid_data`** - Tests template formatting with complete, valid signal data
- **`test_template_formatting_with_null_fields`** - Tests behavior when some enhanced fields are null/default
- **`test_template_formatting_with_zero_values`** - Tests formatting with zero values for numerical fields
- **`test_template_formatting_with_negative_values`** - Tests formatting with negative values (deviations, losses)
- **`test_template_formatting_with_large_numbers`** - Tests formatting with large numerical values
- **`test_template_formatting_with_special_characters_in_symbol`** - Tests HTML escaping and special character handling

#### ✅ **Fallback and Error Handling Tests**

- **`test_fallback_message_creation`** - Tests fallback message creation when template formatting fails
- **`test_template_formatting_with_config_mock`** - Tests template formatting with mocked configuration

## Running the Tests

### Option 1: Using the Test Runner Script

```bash
python run_tests.py
```

### Option 2: Using pytest directly

```bash
# Install test dependencies first
pip install -r requirements-test.txt

# Run all tests
python -m pytest tests/ -v

# Run specific test file
python -m pytest tests/test_arbitrage_signal_template.py -v

# Run with coverage
python -m pytest tests/ --cov=src --cov-report=html
```

## Test Data Validation

The tests validate that the ArbitrageSignal model correctly handles:

### ✅ **Required Fields**

- `symbol` - Trading pair symbol (e.g., "ADA/USDT")
- `buy_exchange` - Exchange for buying (e.g., "gateio")
- `sell_exchange` - Exchange for selling (e.g., "mexc")
- `buy_price` - Buy price (float)
- `sell_price` - Sell price (float)
- `profit_percent` - Profit percentage (float)
- `action` - Trading action ("LONG" or "SHORT")
- `timestamp` - Signal timestamp (datetime)

### ✅ **Enhanced Fields**

- `buy_current_profit` - Current profit for buy position (float)
- `sell_current_profit` - Current profit for sell position (float)
- `buy_deviation` - Deviation percentage for buy exchange (float)
- `sell_deviation` - Deviation percentage for sell exchange (float)
- `sell_limit` - Position limit for sell exchange (float)
- `period` - Period timing information (string, e.g., "4s")
- `exchange_rate` - Exchange rate percentage (float)
- `commission_rate` - Total commission percentage (float)

## Template Output Validation

The tests ensure the formatted message contains:

1. **Proper Turkish labels** - All Turkish terms are correctly displayed
2. **Emoji indicators** - 📗 for long positions, 📕 for short positions, etc.
3. **Numerical formatting** - Percentages, prices, and limits are properly formatted
4. **HTML escaping** - Special characters in exchange names are safely escaped
5. **Complete structure** - All sections (UZUN, KISA, Kur farkı, Grafik) are present

## Error Scenarios Tested

- ❌ **Null/empty required fields** - Tests fail gracefully
- ❌ **Invalid data types** - Tests validate type safety
- ❌ **Template formatting errors** - Tests fallback message creation
- ❌ **Missing configuration** - Tests with mocked config
- ❌ **Special characters** - Tests HTML escaping

## Expected Test Output

When all tests pass, you should see:

```
✅ 11 passed, 12 warnings in 1.27s
```

The warnings are related to Pydantic deprecation notices and don't affect functionality.

## Adding New Tests

When adding new features to the ArbitrageSignal model or template:

1. Add corresponding test methods to `TestArbitrageSignalTemplate`
2. Follow the naming convention: `test_<feature_description>`
3. Include both positive and negative test cases
4. Validate data types, formatting, and error handling
5. Update this README with new test descriptions
