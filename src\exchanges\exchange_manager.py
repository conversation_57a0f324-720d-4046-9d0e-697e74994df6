"""
Exchange manager for handling connections to multiple cryptocurrency exchanges.
"""
import asyncio
import ccxt.async_support as ccxt
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from asyncio_throttle import Throttler
import aiohttp

from src.config.settings import config
from src.database.models import <PERSON>D<PERSON>, db_manager
from src.exchanges.mock_exchange import MockExchangeConnector
from src.utils.logger import logger


class ExchangeConnector:
    """Base connector for exchange operations."""
    
    def __init__(self, exchange_name: str, exchange_config: dict):
        self.name = exchange_name
        self.config = exchange_config
        self.exchange = None
        self.throttler = None
        self.is_connected = False
        self.last_error = None
        self.session = None  # Track aiohttp session for proper cleanup
        
    async def initialize(self):
        """Initialize exchange connection."""
        # Get credentials from config
        credentials = config.get_exchange_credentials(self.name)

        # Map exchange names to CCXT names if needed
        ccxt_name = self._get_ccxt_exchange_name(self.name)

        # Create exchange instance
        if not hasattr(ccxt, ccxt_name):
            raise Exception(f"Exchange {ccxt_name} not supported by CCXT")

        exchange_class = getattr(ccxt, ccxt_name)

        # Create aiohttp session with Windows-compatible settings
        connector = aiohttp.TCPConnector(
            ssl=False,
            limit=100,
            limit_per_host=30,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        self.session = aiohttp.ClientSession(connector=connector)

        # Configure exchange with minimal, working configuration
        exchange_config = {
            'timeout': self.config.timeout * 1000,  # Convert to milliseconds
            'enableRateLimit': True,
            'sandbox': False,  # Set to True for testing
            'headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36'
            },
            'session': self.session
        }

        # Add credentials if available
        if credentials['apiKey'] and credentials['secret']:
            exchange_config.update({
                'apiKey': credentials['apiKey'],
                'secret': credentials['secret'],
            })
            logger.info(f"Initialized {self.name} with API credentials")
        else:
            logger.info(f"Initialized {self.name} with public API only")

        # Try different hostnames if configured
        api_urls = self.config.api_urls if hasattr(self.config, 'api_urls') else []

        # If no custom URLs, try with default configuration first
        if not api_urls:
            try:
                self.exchange = exchange_class(exchange_config)

                # Set up rate limiting
                rate_limit_per_second = self.config.rate_limit / 60  # Convert per minute to per second
                self.throttler = Throttler(rate_limit=rate_limit_per_second)

                # Test connection
                await self._test_connection()
                self.is_connected = True

                await db_manager.update_exchange_status(self.name, "CONNECTED")
                logger.info(f"Successfully connected to {self.name} using default configuration")
                return

            except Exception as e:
                logger.warning(f"Default configuration failed for {self.name}: {e}")
                if self.exchange:
                    await self.exchange.close()
                # Don't close session here as we'll reuse it for custom URLs

        # Try with custom hostnames
        for i, url in enumerate(api_urls):
            try:
                # Create a copy of the config for this attempt
                current_config = exchange_config.copy()

                # Set custom hostname
                hostname = url.replace('https://', '').replace('http://', '')
                current_config['hostname'] = hostname

                self.exchange = exchange_class(current_config)

                # Set up rate limiting
                rate_limit_per_second = self.config.rate_limit / 60  # Convert per minute to per second
                self.throttler = Throttler(rate_limit=rate_limit_per_second)

                # Test connection
                await self._test_connection()
                self.is_connected = True

                await db_manager.update_exchange_status(self.name, "CONNECTED")
                logger.info(f"Successfully connected to {self.name} using {url}")
                return

            except Exception as e:
                logger.warning(f"Attempt {i+1} failed for {self.name} with url {url}: {e}")
                if self.exchange:
                    await self.exchange.close()

        # Clean up session if all attempts failed
        if self.session:
            try:
                await self.session.close()
                logger.info(f"Cleaned up session for failed {self.name} connection")
            except Exception as e:
                logger.warning(f"Error cleaning up session for {self.name}: {e}")
            finally:
                self.session = None

        self.last_error = "All connection attempts failed"
        self.is_connected = False
        await db_manager.update_exchange_status(self.name, "ERROR", self.last_error)
        logger.error(f"Failed to connect to {self.name} after trying all configurations")
        raise Exception(f"Failed to initialize {self.name}")

    def _get_ccxt_exchange_name(self, exchange_name: str) -> str:
        """Map exchange names to CCXT exchange names."""
        name_mapping = {
            'gate': 'gateio',  # Gate.io is 'gateio' in CCXT
            'mexc': 'mexc',
            'lbank': 'lbank'
        }
        return name_mapping.get(exchange_name.lower(), exchange_name.lower())
    
    async def _test_connection(self):
        """Test exchange connection."""
        try:
            async with self.throttler:
                markets = await self.exchange.load_markets(True)
                if not markets:
                    raise Exception("No markets returned from exchange")
                logger.debug(f"Successfully loaded {len(markets)} markets from {self.name}")
        except Exception as e:
            logger.error(f"Connection test failed for {self.name}: {e}")
            logger.debug(f"Exchange object type: {type(self.exchange)}")
            logger.debug(f"Exchange config: {self.exchange.urls if hasattr(self.exchange, 'urls') else 'No URLs'}")
            raise
    
    async def fetch_ticker(self, symbol: str) -> Optional[PriceData]:
        """Fetch ticker data for a symbol."""
        if not self.is_connected:
            return None
            
        try:
            async with self.throttler:
                ticker = await self.exchange.fetch_ticker(symbol)
                
                return PriceData(
                    exchange=self.name,
                    symbol=symbol,
                    price=ticker['last'],
                    volume=ticker['baseVolume'],
                    bid=ticker['bid'],
                    ask=ticker['ask'],
                    timestamp=datetime.now()
                )
                
        except Exception as e:
            logger.error(f"Error fetching ticker for {symbol} from {self.name}: {e}")
            self.last_error = str(e)
            await db_manager.update_exchange_status(self.name, "ERROR", str(e))
            return None
    
    async def fetch_order_book(self, symbol: str, limit: int = 5) -> Optional[Dict]:
        """Fetch order book data."""
        if not self.is_connected:
            return None
            
        try:
            async with self.throttler:
                order_book = await self.exchange.fetch_order_book(symbol, limit)
                return order_book
                
        except Exception as e:
            logger.error(f"Error fetching order book for {symbol} from {self.name}: {e}")
            return None
    
    async def close(self):
        """Close exchange connection."""
        if self.exchange:
            try:
                await self.exchange.close()
            except Exception as e:
                logger.warning(f"Error closing exchange {self.name}: {e}")

        if self.session:
            try:
                await self.session.close()
            except Exception as e:
                logger.warning(f"Error closing session for {self.name}: {e}")
            finally:
                self.session = None

        self.is_connected = False
        logger.info(f"Closed connection to {self.name}")


class ExchangeManager:
    """Manager for multiple exchange connections."""
    
    def __init__(self):
        self.connectors: Dict[str, ExchangeConnector] = {}
        self.enabled_exchanges = []
        
    async def initialize(self):
        """Initialize all enabled exchanges."""
        exchanges_config = config.exchanges
        
        for exchange_name, exchange_config in exchanges_config.items():
            if exchange_config.enabled:
                try:
                    if exchange_config.type == 'mock':
                        connector = MockExchangeConnector(exchange_name, exchange_config)
                    else:
                        connector = ExchangeConnector(exchange_name, exchange_config)
                        
                    await connector.initialize()
                    self.connectors[exchange_name] = connector
                    self.enabled_exchanges.append(exchange_name)
                    
                except Exception as e:
                    logger.error(f"Failed to initialize {exchange_name}: {e}")
                    # Continue with other exchanges
                    continue
        
        if not self.connectors:
            raise Exception("No exchanges could be initialized")
            
        logger.info(f"Initialized {len(self.connectors)} exchanges: {list(self.connectors.keys())}")
    
    async def fetch_all_prices(self, symbol: str) -> List[PriceData]:
        """Fetch prices from all connected exchanges for a symbol."""
        tasks = []
        
        for connector in self.connectors.values():
            if connector.is_connected:
                task = connector.fetch_ticker(symbol)
                tasks.append(task)
        
        if not tasks:
            logger.warning(f"No connected exchanges available for {symbol}")
            return []
        
        # Execute all requests concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out None results and exceptions
        prices = []
        for result in results:
            if isinstance(result, PriceData):
                prices.append(result)
            elif isinstance(result, Exception):
                logger.error(f"Error in concurrent price fetch: {result}")
        
        return prices
    
    async def fetch_prices_batch(self, symbols: List[str]) -> Dict[str, List[PriceData]]:
        """Fetch prices for multiple symbols from all exchanges."""
        results = {}
        
        # Create tasks for all symbol-exchange combinations
        tasks = []
        task_info = []
        
        for symbol in symbols:
            for connector in self.connectors.values():
                if connector.is_connected:
                    task = connector.fetch_ticker(symbol)
                    tasks.append(task)
                    task_info.append((symbol, connector.name))
        
        if not tasks:
            logger.warning("No connected exchanges available for batch fetch")
            return results
        
        # Execute all requests concurrently
        task_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Group results by symbol
        for i, result in enumerate(task_results):
            symbol, exchange = task_info[i]
            
            if symbol not in results:
                results[symbol] = []
            
            if isinstance(result, PriceData):
                results[symbol].append(result)
            elif isinstance(result, Exception):
                logger.error(f"Error fetching {symbol} from {exchange}: {result}")
        
        return results
    
    def get_connected_exchanges(self) -> List[str]:
        """Get list of currently connected exchanges."""
        return [
            name for name, connector in self.connectors.items() 
            if connector.is_connected
        ]
    
    def get_exchange_status(self) -> Dict[str, Dict]:
        """Get status of all exchanges."""
        status = {}
        for name, connector in self.connectors.items():
            status[name] = {
                'connected': connector.is_connected,
                'last_error': connector.last_error,
                'enabled': name in self.enabled_exchanges
            }
        return status
    
    async def reconnect_failed_exchanges(self):
        """Attempt to reconnect failed exchanges."""
        for name, connector in self.connectors.items():
            if not connector.is_connected:
                try:
                    logger.info(f"Attempting to reconnect to {name}")
                    await connector.initialize()
                except Exception as e:
                    logger.error(f"Reconnection failed for {name}: {e}")
    
    async def health_check(self) -> Dict[str, bool]:
        """Perform health check on all exchanges."""
        health_status = {}
        
        for name, connector in self.connectors.items():
            try:
                if connector.is_connected:
                    # Try to fetch a simple ticker to test connectivity
                    test_symbol = "BTC/USDT"  # Most exchanges should have this
                    result = await connector.fetch_ticker(test_symbol)
                    health_status[name] = result is not None
                else:
                    health_status[name] = False
                    
            except Exception as e:
                logger.error(f"Health check failed for {name}: {e}")
                health_status[name] = False
                await db_manager.update_exchange_status(name, "ERROR", str(e))
        
        return health_status
    
    async def close_all(self):
        """Close all exchange connections."""
        tasks = []
        for connector in self.connectors.values():
            tasks.append(connector.close())
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        self.connectors.clear()
        self.enabled_exchanges.clear()
        logger.info("Closed all exchange connections")


# Global exchange manager instance
exchange_manager = ExchangeManager()
