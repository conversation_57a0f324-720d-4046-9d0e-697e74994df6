["tests/test_arbitrage_signal_template.py::TestArbitrageSignalTemplate::test_fallback_message_creation", "tests/test_arbitrage_signal_template.py::TestArbitrageSignalTemplate::test_required_fields_not_empty", "tests/test_arbitrage_signal_template.py::TestArbitrageSignalTemplate::test_signal_data_types", "tests/test_arbitrage_signal_template.py::TestArbitrageSignalTemplate::test_template_formatting_with_config_mock", "tests/test_arbitrage_signal_template.py::TestArbitrageSignalTemplate::test_template_formatting_with_large_numbers", "tests/test_arbitrage_signal_template.py::TestArbitrageSignalTemplate::test_template_formatting_with_negative_values", "tests/test_arbitrage_signal_template.py::TestArbitrageSignalTemplate::test_template_formatting_with_null_fields", "tests/test_arbitrage_signal_template.py::TestArbitrageSignalTemplate::test_template_formatting_with_special_characters_in_symbol", "tests/test_arbitrage_signal_template.py::TestArbitrageSignalTemplate::test_template_formatting_with_valid_data", "tests/test_arbitrage_signal_template.py::TestArbitrageSignalTemplate::test_template_formatting_with_zero_values", "tests/test_arbitrage_signal_template.py::TestArbitrageSignalTemplate::test_valid_signal_creation"]