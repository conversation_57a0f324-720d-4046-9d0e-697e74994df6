/* Console UI Styles for Arbitrage Bot - Textual-Compatible */

/* Color definitions */
$primary: #b48ead;
$secondary: #5e81ac;
$accent: #a3be8c;
$success: #8fbcbb;
$warning: #ebcb8b;
$error: #bf616a;
$text1: #eceff4;
$text2: #4c566a;
$background: #080829;
$surface: #080829;

/* Main app styling */
App {
  background: $background;
  color: $text1;
}

/* Layout containers */
Screen {
  layout: grid;
  grid-size: 2;
  grid-gutter: 1;
  padding: 1;
}

#left-panel {
  width: 30%;
  border: solid $primary;
  padding: 1;
}

#right-panel {
  width: 70%;
  border: solid $primary;
  padding: 1;
}

/* Typography */
.title {
  content-align: center middle;
  text-style: bold;
  color: $accent;
  margin-bottom: 1;
}

.section-title {
  text-style: bold underline;
  color: $primary;
  margin: 1 0;
}

/* Navigation */
#nav-menu {
  height: 8;
  border: solid $primary;
  margin-bottom: 1;
}

#nav-menu > ListItem {
  padding: 0 1;
}

#nav-menu > ListItem:hover {
  background: $primary;
  color: $background;
}

/* Status indicators */
#bot-status {
  border: solid $success;
  padding: 1;
  margin-bottom: 1;
}

.status-active {
  color: $success;
  text-style: bold;
}

.status-inactive {
  color: $error;
  text-style: bold;
}

/* Buttons */
Button {
  width: 100%;
  margin-bottom: 1;
}

Button:focus {
  text-style: underline;
}

#toggle-bot {
  background: $success;
  color: auto;
}

#toggle-bot:hover {
  background: $success 80%;
}

#config-btn {
  background: $warning;
  color: auto;
}

#config-btn:hover {
  background: $warning 80%;
}

#quit-btn {
  background: $error;
  color: auto;
}

#quit-btn:hover {
  background: $error 80%;
}

/* Log display */
#log-display {
  border: solid $primary;
  height: 100%;
  overflow-y: auto;
  scrollbar-gutter: stable;
}

.log-entry {
  margin: 0;
  padding: 0;
}

.log-info {
  color: $text1;
}

.log-warning {
  color: $warning;
}

.log-error {
  color: $error;
}

.log-success {
  color: $success;
}

/* Dashboard components */
#dashboard-grid {
  layout: grid;
  grid-size: 1 3;
  grid-gutter: 1;
}

.pair-title {
  text-style: bold;
  color: $accent;
  margin-bottom: 1;
}

#trading-pairs-section {
  width: 50%;
  border: solid $primary;
  padding: 1;
}

#trading-pairs-container {
  height: 100%;
  overflow-y: auto;
}

#opportunities-table {
  height: 15;
  border: solid $success;
}

#exchange-status {
  width: 50%;
  border: solid $warning;
  padding: 1;
}

#performance-metrics {
  border: solid $secondary;
  padding: 1;
  height: 10;
}

/* Tables */
DataTable {
  background: $surface;
}

DataTable > .datatable--header {
  background: $primary;
  color: $background;
  text-style: bold;
}

DataTable > .datatable--cursor {
  background: $primary;
  opacity: 0.3;
}

/* Form elements */
Input {
  background: $surface;
  color: $text1;
  border: panel $text2;
}

Input:focus {
  border: panel $primary;
}

Select {
  background: $surface;
  color: $text1;
  border: panel $text2;
}

Select:focus {
  border: panel $primary;
}

/* Utility classes */
.text-center {
  content-align: center middle;
}

.text-right {
  content-align: right middle;
}

.mt-1 {
  margin-top: 1;
}

.mb-1 {
  margin-bottom: 1;
}

.ml-1 {
  margin-left: 1;
}

.mr-1 {
  margin-right: 1;
}

.p-1 {
  padding: 1;
}
