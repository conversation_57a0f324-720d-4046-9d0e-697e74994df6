#!/usr/bin/env python3
"""
Test runner script for the arbitrage bot unit tests.
"""
import subprocess
import sys
from pathlib import Path


def install_test_dependencies():
    """Install test dependencies if not already installed."""
    try:
        import pytest
        print("✓ pytest is already installed")
    except ImportError:
        print("Installing test dependencies...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements-test.txt"
        ])
        print("✓ Test dependencies installed")


def run_tests():
    """Run the unit tests."""
    print("Running ArbitrageSignal and Template Tests...")
    print("=" * 60)
    
    # Run specific test file
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/test_arbitrage_signal_template.py",
        "-v", "--tb=short"
    ], cwd=Path(__file__).parent)
    
    if result.returncode == 0:
        print("\n" + "=" * 60)
        print("✅ All tests passed successfully!")
    else:
        print("\n" + "=" * 60)
        print("❌ Some tests failed. Check the output above for details.")
    
    return result.returncode


if __name__ == "__main__":
    try:
        install_test_dependencies()
        exit_code = run_tests()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\nTest execution interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nError running tests: {e}")
        sys.exit(1)
